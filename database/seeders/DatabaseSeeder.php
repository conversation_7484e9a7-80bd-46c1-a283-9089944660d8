<?php

namespace Database\Seeders;

use Database\Seeders\CoreSeeder;
use Database\Seeders\MenuSeeder;
use Database\Seeders\ModulesSubModulesPermissionsSeeder;
use Database\Seeders\RoleSeeder;
use Database\Seeders\UserSeeder;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CoreSeeder::class,
            UserSeeder::class,
            RoleSeeder::class,
            ModulesSubModulesPermissionsSeeder::class,
            MenuSeeder::class,
        ]);
    }
}
