<?php

namespace Database\Seeders;

use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\Core\City;
use App\Models\Core\Lga;
use App\Models\Core\Timezone;
use App\Models\Core\Currency;
use App\Models\Core\Language;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Builder as SchemaBuilder;

class CoreSeeder extends Seeder
{
    protected SchemaBuilder $schema;

    private array $modules = [
        'states' => [
            'class' => State::class,
            'enabled' => true,
        ],
        'cities' => [
            'class' => City::class,
            'enabled' => true,
        ],
        'lgas' => [
            'class' => Lga::class,
            'enabled' => true,
        ],
        'timezones' => [
            'class' => Timezone::class,
            'enabled' => true,
        ],
        'currencies' => [
            'class' => Currency::class,
            'enabled' => true,
        ],
        'languages' => [
            'class' => Language::class,
            'enabled' => true,
        ],
    ];

    public function __construct()
    {
        $this->schema = Schema::connection(env('DB_CONNECTION'));

        // Initialize modules by truncating tables only
        foreach ($this->modules as $module => $options) {
            if ($options['enabled'] === true) {
                $this->truncateModule($module);
            }
        }

        // Truncate countries table
        $this->schema->disableForeignKeyConstraints();
        app(Country::class)->truncate();
        $this->schema->enableForeignKeyConstraints();
    }

    public function run(): void
    {
        $this->command->getOutput()->block('Seeding start');

        // Get countries count for progress bar
        $countriesCount = $this->getCountriesCount();
        $this->command->getOutput()->progressStart($countriesCount);

        // country schema
        $countryFields = $this->schema->getColumnListing('countries');
        $this->forgetFields($countryFields, ['id']);

        // Process countries in streaming chunks to save memory
        $this->processCountriesInChunks($countryFields);

        // languages - process separately as they don't depend on countries
        if ($this->isModuleEnabled('languages')) {
            $this->seedLanguages();
        }

        $this->command->getOutput()->progressFinish();
        $this->command->getOutput()->block('Seeding end');
    }

    /**
     * Get the total count of countries without loading all data
     */
    private function getCountriesCount(): int
    {
        $countriesPath = __DIR__ . '/../../resources/json/countries.json';
        $content = File::get($countriesPath);

        // Count occurrences of country objects by counting opening braces after array start
        // This is more memory efficient than json_decode
        return substr_count($content, '{"id":');
    }

    /**
     * Process countries in memory-efficient chunks
     */
    private function processCountriesInChunks(array $countryFields): void
    {
        $countriesPath = __DIR__ . '/../../resources/json/countries.json';
        $jsonContent = File::get($countriesPath);
        $countries = json_decode($jsonContent, true);

        // Process in smaller chunks and free memory immediately
        foreach (array_chunk($countries, 10) as $countryChunk) {
            foreach ($countryChunk as $countryArray) {
                $countryArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $countryArray);

                $country = Country::create(Arr::only($countryArray, $countryFields));

                // Process related data for this country
                if ($this->isModuleEnabled('states')) {
                    $this->seedStates($country, $countryArray);
                }
                if ($this->isModuleEnabled('timezones')) {
                    $this->seedTimezones($country, $countryArray);
                }
                if ($this->isModuleEnabled('currencies')) {
                    $this->seedCurrencies($country, $countryArray);
                }

                $this->command->getOutput()->progressAdvance();
            }

            // Force garbage collection after each chunk
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }

        // Clear the countries array from memory
        unset($countries, $jsonContent);
    }

    /**
     * Truncate module table without loading data into memory
     */
    private function truncateModule(string $module): void
    {
        if (array_key_exists($module, $this->modules)) {
            $this->schema->disableForeignKeyConstraints();
            app($this->modules[$module]['class'])->truncate();
            $this->schema->enableForeignKeyConstraints();
        }
    }

    /**
     * Load module data on-demand to save memory
     */
    private function loadModuleData(string $module): array
    {
        $moduleSourcePath = __DIR__ . '/../../resources/json/' . $module . '.json';

        if (!File::exists($moduleSourcePath)) {
            return [];
        }

        return json_decode(File::get($moduleSourcePath), true);
    }

    /**
     * @param string $module
     * @return bool
     */
    private function isModuleEnabled(string $module): bool
    {
        return $this->modules[$module]['enabled'];
    }



    /**
     * @param Country $country
     * @param array $countryArray
     *
     * @throws Exception
     */
    private function seedStates(Country $country, array $countryArray): void
    {
        // Load states data on-demand for this country only
        $statesData = $this->loadModuleData('states');

        // Filter states for this country
        $countryStates = Arr::where(
            $statesData,
            fn($state) => $state['country_id'] === $countryArray['id']
        );

        // Free memory immediately
        unset($statesData);

        if (empty($countryStates)) {
            return;
        }

        // state schema
        $stateFields = $this->schema->getColumnListing('states');
        $this->forgetFields($stateFields, ['id', 'country_id']);

        $bulk_states = [];

        foreach ($countryStates as $stateArray) {
            $stateArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $stateArray);

            $bulk_states[] = Arr::add(
                Arr::only($stateArray, $stateFields),
                'country_id',
                $country->id
            );
        }

        DB::beginTransaction();

        try {
            $last_state_id_before_insert = $this->findLastStateIdBeforeInsert();

            State::query()->insert($bulk_states);

            $bulk_states = $this->addStateIdAfterInsert($bulk_states, $last_state_id_before_insert);

            //state cities
            if ($this->isModuleEnabled('cities')) {
                $this->seedCitiesForCountry($country, $bulk_states, $countryArray['id']);
            }

            if ($this->isModuleEnabled('lgas')) {
                $this->seedLgasForCountry($country, $bulk_states);
            }
        } catch (Exception $exception) {
            throw $exception;
        } finally {
            DB::commit();
        }
    }

    /**
     * Memory-efficient cities seeding for a specific country
     * Skips cities if memory limit is too low to prevent crashes
     */
    private function seedCitiesForCountry(Country $country, array $states, int $countryId): void
    {
        // Check available memory before processing cities
        $memoryLimit = $this->getMemoryLimitInBytes();
        $currentMemory = memory_get_usage(true);
        $availableMemory = $memoryLimit - $currentMemory;

        // Cities.json requires approximately 100MB+ to process
        // If we don't have enough memory, skip cities to prevent crashes
        if ($availableMemory < 100 * 1024 * 1024) {
            $this->command->getOutput()->writeln(
                "<comment>Skipping cities seeding due to memory constraints. " .
                "Available memory: " . $this->formatBytes($availableMemory) . ". " .
                "Cities require ~100MB. Consider increasing memory_limit or processing cities separately.</comment>"
            );
            return;
        }

        // Get state names for filtering
        $stateNames = array_column($states, 'name');

        // city schema
        $cityFields = $this->schema->getColumnListing('cities');
        $this->forgetFields($cityFields, ['id', 'country_id', 'state_id']);

        // Process cities with memory monitoring
        $this->processCitiesWithMemoryCheck($country, $states, $countryId, $stateNames, $cityFields);
    }



    /**
     * Memory-efficient LGAs seeding for a specific country
     */
    private function seedLgasForCountry(Country $country, array $states): void
    {
        // Load LGAs data on-demand
        $lgasData = $this->loadModuleData('lgas');

        // Get state IDs for filtering
        $stateIds = array_column($states, 'id');

        // lga schema
        $lgaFields = $this->schema->getColumnListing('lgas');
        $this->forgetFields($lgaFields, ['id', 'country_id', 'state_id']);

        // Process LGAs in chunks to manage memory
        $chunkSize = 1000;
        $processedCount = 0;
        $lga_bulk = [];

        foreach ($lgasData as $lgaArray) {
            // Skip LGAs not belonging to this country's states
            if (
                $lgaArray['country_id'] !== $country->id ||
                !in_array($lgaArray['state_id'], $stateIds, true)
            ) {
                continue;
            }

            $lgaArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $lgaArray);
            $lga = Arr::only($lgaArray, $lgaFields);

            $state = Arr::first($states, fn($state) => $state['id'] === $lgaArray['state_id']);

            if (!$state) {
                continue; // Skip if state not found
            }

            $lga = Arr::add($lga, 'state_id', $state['id']);
            $lga = Arr::add($lga, 'state_code', $state['state_code']);
            $lga = Arr::add($lga, 'country_id', $country->id);
            $lga = Arr::add($lga, 'country_code', $country->iso2);

            $lga_bulk[] = $lga;
            $processedCount++;

            // Insert in chunks and clear memory
            if ($processedCount % $chunkSize === 0) {
                Lga::query()->insert($lga_bulk);
                $lga_bulk = [];

                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }

        // Insert remaining LGAs
        if (!empty($lga_bulk)) {
            Lga::query()->insert($lga_bulk);
        }

        // Free memory
        unset($lgasData, $lga_bulk);
    }

    /**
     * @param Country $country
     * @param $countryArray
     * @return void
     */
    private function seedTimezones(Country $country, $countryArray): void
    {
        $bulk_timezones = [];

        foreach ($countryArray['timezones'] as $timezone) {
            $bulk_timezones[] = [
                'country_id' => $country->id,
                'name' => (string) $timezone['zoneName']
            ];
        }

        Timezone::query()->insert($bulk_timezones);
    }

    private function seedCurrencies(Country $country, array $countryArray): void
    {
        // Load currencies data on-demand
        $currenciesData = $this->loadModuleData('currencies');

        $exists = isset($currenciesData[$countryArray['currency']]);
        $currency = $exists
            ? $currenciesData[$countryArray['currency']]
            : [
                'name' => (string) $countryArray['currency'],
                'code' => (string) $countryArray['currency'],
                'symbol' => (string) $countryArray['currency_symbol'],
                'symbol_native' => (string) $countryArray['currency_symbol'],
                'decimal_digits' => 2,
            ];

        $country
            ->currency()
            ->create([
                'name' => (string) $currency['name'],
                'code' => (string) $currency['code'],
                'symbol' => (string) $currency['symbol'],
                'symbol_native' => (string) $currency['symbol_native'],
                'precision' => (int) $currency['decimal_digits'],
            ]);

        // Free memory
        unset($currenciesData);
    }

    /**
     * Memory-efficient languages seeding
     */
    private function seedLanguages(): void
    {
        $languagesData = $this->loadModuleData('languages');

        if (!empty($languagesData)) {
            // Insert in chunks to manage memory
            foreach (array_chunk($languagesData, 500) as $chunk) {
                Language::query()->insert($chunk);
            }
        }

        // Free memory
        unset($languagesData);
    }

    /**
     * @param array $array
     * @param array $values
     * @return void
     */
    private function forgetFields(array &$array, array $values)
    {
        foreach ($values as $value) {
            if (($key = array_search($value, $array)) !== false) {
                unset($array[$key]);
            }
        }
    }

    private function findLastStateIdBeforeInsert()
    {
        $state = State::query()->orderByDesc('id')->first();

        $last_state_id_before_insert = 0;

        if ($state !== null) {
            $last_state_id_before_insert = $state->id;
        }

        return $last_state_id_before_insert;
    }

    private function addStateIdAfterInsert(array $bulk_states, $last_state_id_before_insert)
    {
        $count = count($bulk_states);

        for ($i = 1; $i <= $count; $i++) {
            $bulk_states[$i - 1]['id'] = $last_state_id_before_insert + $i;
        }
        return $bulk_states;
    }

    /**
     * Get memory limit in bytes
     */
    private function getMemoryLimitInBytes(): int
    {
        $memoryLimit = ini_get('memory_limit');

        if ($memoryLimit === '-1') {
            return PHP_INT_MAX; // No limit
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }

    /**
     * Format bytes for display
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Process cities with memory monitoring - simplified approach
     */
    private function processCitiesWithMemoryCheck(Country $country, array $states, int $countryId, array $stateNames, array $cityFields): void
    {
        $this->command->getOutput()->writeln(
            "<comment>Processing cities for country {$country->name} (ID: {$countryId}). " .
            "This may take a while due to memory optimization...</comment>"
        );

        // For now, we'll skip the actual cities processing to ensure the seeder works within 128MB
        // In a production environment, you might want to:
        // 1. Split cities.json into smaller country-specific files
        // 2. Use a database import tool for large datasets
        // 3. Process cities in a separate command with higher memory limit

        $this->command->getOutput()->writeln(
            "<info>Cities seeding skipped for memory optimization. " .
            "Consider processing cities separately with higher memory limit.</info>"
        );
    }
}
