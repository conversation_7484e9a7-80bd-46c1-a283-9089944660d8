<?php

namespace Database\Seeders;

use App\Models\Web\Shared\Module;
use Illuminate\Database\Seeder;

class ModulesSubModulesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $abilities = [
            'create',
            'read',
            'update',
            'delete',
        ];

        $subModulesByModules = [
            'system-administration' => [
                'access-control' => [
                    'roles',
                    'permissions',
                ],
                'users' => '',
            ],
            'reports' => [],
        ];

        // Assuming you have a SubModule model and a relationship set up in Module
        foreach ($subModulesByModules as $moduleCode => $subModules) {
            $module = Module::updateOrCreate(
                ['code' => str_replace('_', '-', $moduleCode)],
                [
                    'name' => ucwords(string: str_replace(['-', '_'], [' ', ' & '], $moduleCode)),
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );

            foreach ($subModules as $subModuleCode => $menus) {
                // $subModuleCode is the submodule code, $menus is an array of children
                $subModule = $module->subModules()->updateOrCreate(
                    ['code' => $subModuleCode],
                    [
                        'name' => ucwords(str_replace('-', ' ', $subModuleCode)),
                        'description' => null,
                        'is_active' => true,
                        'created_by' => 1,
                        'modified_by' => 1,
                    ]
                );

                if (is_array($menus)) {
                    foreach ($menus as $index => $menuName) {
                        foreach ($abilities as $ability) {
                            $subModule->permissions()->updateOrCreate(
                                ['action' => "{$ability}.{$menuName}"],
                                [
                                    'name' => ucwords(str_replace('.', ' ', "{$ability}.{$menuName}")),
                                    'description' => null,
                                    'is_active' => true,
                                    'created_by' => 1,
                                    'modified_by' => 1,
                                ]
                            );
                        }
                    }
                } else {
                    foreach ($abilities as $ability) {
                        $subModule->permissions()->updateOrCreate(
                            ['action' => "{$ability}.{$subModuleCode}"],
                            [
                                'name' => ucwords(str_replace('.', ' ', subject: "{$ability}.{$subModuleCode}")),
                                'description' => null,
                                'is_active' => true,
                                'created_by' => 1,
                                'modified_by' => 1,
                            ]
                        );
                    }
                }
            }
        }
    }
}
