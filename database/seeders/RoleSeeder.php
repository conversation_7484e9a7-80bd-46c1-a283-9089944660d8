<?php

namespace Database\Seeders;

use App\Models\Web\AccessControl\Role;
use App\Models\User;
use App\Services\Shared\UserService;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function __construct(protected UserService $userService)
    {
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            ['Administrator', 'admin', 'web', 'This is the role of the administrator'],
            ['Developer', 'dev', 'web', 'This is the role of the developer'],
            ['Analyst', 'analyst', 'web', 'This is the role of the system analyst'],
            ['Support', 'support', 'web', 'This is the role of the support giver'],
            ['Guest', 'guest', 'web', 'This is the role of the guest'],
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(
                ['name' => $role[0]],
                [
                    'slug' => $role[1],
                    'guard_name' => $role[2],
                    'description' => $role[3],
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }

        $users = User::where('id', '<', 6)->get();
        foreach ($users as $user) {
            if ($user->id >= 1 && $user->id <= 5) {
                $this->userService->syncRoles($user, [2]);
            }
        }
    }
}
