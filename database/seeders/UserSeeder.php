<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $is_in_production = config('settings.app.env') === 'production';
        $default_password = 'password';

        $accounts = [
            [
                '001',
                '<PERSON><PERSON><PERSON>',
                '<EMAIL>',
                '+*************',
                bcrypt($is_in_production ? 'maarufadam' : $default_password),
            ],
            [
                '002',
                '<PERSON>',
                '<EMAIL>',
                '+*************',
                bcrypt($is_in_production ? 'khaleefa' : $default_password),
            ],
            [
                '003',
                '<PERSON><PERSON><PERSON>',
                '<EMAIL>',
                '+*************',
                bcrypt($is_in_production ? 'tripple99' : $default_password),
            ],
        ];

        foreach ($accounts as $account) {
            User::updateOrCreate(
                ['identifier' => $account[0]],
                [
                    'name' => $account[1],
                    'email' => $account[2],
                    'phone_no' => $account[3],
                    'profile_picture' => null,
                    'is_active' => true,
                    'email_verified_at' => now(),
                    'password' => $account[4],
                    'first_access' => null,
                    'last_access' => null,
                    'remember_token' => null,
                ]
            );
        }
    }
}
