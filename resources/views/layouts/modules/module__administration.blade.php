@canany(['read.users','read.roles','read.permissions'])
<div class="menu-item pt-5">
    <!--begin:Menu content-->
    <div class="menu-content">
        <span class="menu-heading fw-bold text-uppercase fs-7">SYSTEM ADMINISTRATION</span>
    </div>
    <!--end:Menu content-->
</div>
@endcanany

@role('dev')
    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
        <a href="{{ route('telescope') }}" target="_blank" class="menu-link {{ request()->is('telescope*') ? 'active' : '' }}">
            <span class="menu-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                    <path style="fill: #4040c8;" d="M0 40a39.87 39.87 0 0 1 11.72-28.28A40 40 0 1 1 0 40zm34 10a4 4 0 0 1-4-4v-2a2 2 0 1 0-4 0v2a4 4 0 0 1-4 4h-2a2 2 0 1 0 0 4h2a4 4 0 0 1 4 4v2a2 2 0 1 0 4 0v-2a4 4 0 0 1 4-4h2a2 2 0 1 0 0-4h-2zm24-24a6 6 0 0 1-6-6v-3a3 3 0 0 0-6 0v3a6 6 0 0 1-6 6h-3a3 3 0 0 0 0 6h3a6 6 0 0 1 6 6v3a3 3 0 0 0 6 0v-3a6 6 0 0 1 6-6h3a3 3 0 0 0 0-6h-3zm-4 36a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM21 28a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"></path>
                </svg>
            </span>

            <span class="menu-title">Telescope</span>
            <span>
                <i class="ki-outline ki-arrow-right fs-3"></i>
            </span>
            <!--begin:Menu link-->
        </a>
        <!--end:Menu link-->
    </div>
@endrole

@can('read.users')
    <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
        <a href="{{ route('users.index') }}" class="menu-link {{ request()->is('users*') ? 'active' : '' }}">
            <span class="menu-icon">
                <i class="ki-outline ki-people fs-2"></i>
            </span>

            <span class="menu-title">Users</span>
            <span>
                <i class="ki-outline ki-arrow-right fs-3"></i>
            </span>
            <!--begin:Menu link-->
        </a>
        <!--end:Menu link-->
    </div>
@endcan

@canany(['read.roles','read.permissions'])
    @include('layouts.modules.section__access_control')
@endcanany
