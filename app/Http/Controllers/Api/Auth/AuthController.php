<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Resources\Shared\UserResource;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        return DB::transaction(function () use ($request) {
            $request->validate([
                'email' => 'required|string|email',
                'password' => 'required|string|min:8'
            ]);


            if (auth()->attempt($request->only('email', 'password'))) {
                $user = User::where('id', auth()->user()->id)->first();

                if (!$user->is_active) {
                    return $this->error('Your account is disabled. Please contact the administrators', 401);
                }

                $accessToken = $user->createToken('access_token')->plainTextToken;
                $tokenParts = explode('|', $accessToken);
                $plainToken = $tokenParts[1] ?? null;
                DB::table('personal_access_tokens')
                ->where('token', hash('sha256', $plainToken))
                ->update([
                    'expires_at' => Carbon::now()->addHours(config('settings.security.token_expiration_hours'))
                ]);

                return response()->json(
                    [
                        'access_token' => $accessToken,
                        'token_type' => 'Bearer',
                        'user' => new UserResource($user),
                        'pm' => 'None'
                    ],
                    200
                );
            }

            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        });
    }

    public function logout()
    {
        auth()->user()->tokens()->delete();

        return $this->success(null);
    }
}
