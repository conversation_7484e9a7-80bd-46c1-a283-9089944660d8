<?php

namespace App\Http\Controllers\Api;

use App\Classes\FileUpload;
use App\Http\Controllers\Controller;
use App\Traits\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class FileUploadController extends Controller
{
    use JsonResponse;

    public function getFile(string $filename, $visibility = 'public')
    {
        $folder = explode('_', $filename)[0];
        $filepath =  "public/files/$folder/$filename";
        if (!Storage::disk($visibility)->exists($filepath)) {
            return $this->error('File not found.', 404);
        }
        return Storage::response($filepath);
    }
}
