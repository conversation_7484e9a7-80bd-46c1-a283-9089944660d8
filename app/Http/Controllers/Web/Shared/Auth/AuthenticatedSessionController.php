<?php

namespace App\Http\Controllers\Web\Shared\Auth;

use App\Exceptions\Auth\UnauthorizedAuthDomainException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Web\Shared\Auth\WebLoginRequest;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class AuthenticatedSessionController extends Controller
{
    public static $auth_domains = [
        'example.com',
    ];

    /**
     * Display the login view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // return view('shared.auth.unauthorized-domain');
        return view('shared.auth.login');
    }

    /**
     * Handle an incoming authentication request.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(WebLoginRequest $request)
    {
        $credential = $request->input('credential');

        // Only check domain if credential is an email
        if (filter_var($credential, FILTER_VALIDATE_EMAIL)) {
            $domain = explode('@', $credential)[1];

            if (!(in_array($domain, self::$auth_domains) || Str::endsWith($domain, self::$auth_domains[0]))) {
                throw new UnauthorizedAuthDomainException();
            }
        }

        $request->authenticate();

        $request->session()->regenerate();

        return redirect()->intended(RouteServiceProvider::HOME);
    }

    /**
     * Destroy an authenticated session.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
