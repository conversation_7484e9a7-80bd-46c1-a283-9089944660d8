<?php

namespace App\Http\Controllers\Web\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Web\Shared\UserUpdateRequest;
use App\Http\Requests\Web\Shared\UserStoreRequest;
use App\Models\User;
use App\Services\AccessControl\RoleService;
use App\Services\Shared\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Storage;
use Symfony\Component\HttpFoundation\Response;

class UserController extends Controller
{
    public const AVATAR_PATH = 'files/avatar/';
    private const PER_PAGE = 25;

    public function __construct(
        protected RoleService $roleService,
        protected UserService $service,
    ) {
        $this->middleware('role:admin|dev');
    }

    public function index(Request $request): View
    {
        return view('shared.users.index', [
            'users' => $this->service->getAllUsers(),
            'roles' => $this->roleService->getAllRoles(),
        ]);
    }

    public function store(UserStoreRequest $request): Response|ResponseFactory
    {
        $data = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $identifier = Str::lower($data['identifier']);
            $filename = uniqid("avatar_{$identifier}_") . '.' . $file->getClientOriginalExtension();
            if (
                $request->file('avatar')->storePubliclyAs(
                    self::AVATAR_PATH,
                    $filename,
                    'public'
                ) !== false
            ) {
                $data['profile_picture'] = $filename;
            }
        }

        // Ensure all fields are present
        $user = User::create([
            'identifier' => $data['identifier'],
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_no' => $data['phone_no'],
            'password' => bcrypt($data['password']),
            'profile_picture' => $data['profile_picture'] ?? null,
            'is_active' => $data['is_active'] ?? 1,
        ]);

        if ($user) {
            $request->session()->flash('success', 'User added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function show(Request $request, User $user): View
    {
        return view('shared.users.show', [
            'user' => $user
        ]);
    }

    public function edit(Request $request, User $user): View
    {
        return view('shared.users.edit', [
            'user' => $user,
            'roles' => $this->roleService->getAllRoles(),
        ]);
    }

    public function update(UserUpdateRequest $request, User $user): Response|ResponseFactory
    {
        $data = $request->validated();

        if (isset($request->avatar_remove) && $request->avatar_remove == 1) {
            if ($user->profile_picture && Storage::disk('public')->exists(self::AVATAR_PATH . $user->profile_picture)) {
                Storage::disk('public')->delete(self::AVATAR_PATH . $user->profile_picture);
            }
            $user->profile_picture = null;
            $user->save();
        }

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Optionally delete old image
            if ($user->profile_picture && Storage::disk('public')->exists(self::AVATAR_PATH . $user->profile_picture)) {
                Storage::disk('public')->delete(self::AVATAR_PATH . $user->profile_picture);
            }

            $file = $request->file('avatar');
            $identifier = Str::lower($user->identifier);
            $filename = uniqid("avatar_{$identifier}_") . '.' . $file->getClientOriginalExtension();
            if (
                $request->file('avatar')->storePubliclyAs(
                    self::AVATAR_PATH,
                    $filename,
                    'public'
                ) !== false
            ) {
                $data['profile_picture'] = $filename;
            }
        }

        // If password is blank, don't update it
        if (empty($data['password'])) {
            unset($data['password']);
        } else {
            $data['password'] = bcrypt($data['password']);
        }

        $updated = $this->service->update($user, $data);
        if ($updated) {
            $request->session()->flash('success', 'User updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, User $user): RedirectResponse
    {
        $user->delete();

        return redirect()->route('users.index');
    }
}
