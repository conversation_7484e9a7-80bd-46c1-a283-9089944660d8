<?php

namespace App\Http\Requests\Web\Shared;

use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'avatar' => ['required', 'mimes:png,jpg,jpeg', 'max:2048'],
            'identifier' => ['required', 'string', 'unique:users,identifier'],
            'phone_no' => ['required', 'string', 'max:255', 'unique:users,phone_no'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ];
    }

    public function messages(): array
    {
        return [
            'avatar.required' => 'A profile picture is required.',
        ];
    }
}
