<?php

namespace App\Repositories\Shared;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class UserRepository
{
    public function __construct(protected User $user)
    {
        //
    }


    public function create(array $data): User
    {
        return $this->user->create($data);
    }

    public function update(User $user, array $data): User
    {
        $user->update($data);

        return $user->refresh();
    }

    public function delete(User $user): bool
    {
        return $user->delete();
    }

    public function getAllUsers(): Collection
    {
        return $this->user->with('roles', 'roles.permissions')->get();
    }

    public function getUserById(int $id): User
    {
        return $this->user->find($id);
    }

    public function getUserByEmail(string $email): User
    {
        return $this->user->where('email', $email)->first();
    }

    public function getUserByPhoneNo(string $phone_no): User
    {
        return $this->user->where('phone_no', $phone_no)->first();
    }

    public function getUserByIdentifier(string $identifier): User
    {
        return $this->user->where('identifier', $identifier)->first();
    }
}
