<?php

namespace App\Services\Shared;

use App\Models\Web\AccessControl\Role;
use App\Models\User;
use App\Repositories\Shared\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function __construct(protected UserRepository $repo)
    {
    }

    public function create(array $data): User
    {
        $data['password'] = Hash::make($data['password']);

        return $this->repo->create($data);
    }

    public function getAllUsers(): Collection
    {
        return $this->repo->getAllUsers();
    }

    public function update(User $user, array $data): User
    {
        // if (isset($data['password']) && $data['password']) {
        //     $data['password'] = Hash::make($data['password']);
        // } else {
        //     unset($data['password']);
        // }
        $updatedUser = $this->repo->update($user, $data);
        // Sync role if provided
        if (array_key_exists('role_id', $data) && $data['role_id']) {
            $this->syncRoles($updatedUser, [$data['role_id']]);
        }
        return $updatedUser;
    }

    public function delete(User $user): bool
    {
        return $this->repo->delete($user);
    }

    public function getUserById(int $id): User
    {
        return $this->repo->getUserById($id);
    }

    public function getUserByEmail(string $email): User
    {
        return $this->repo->getUserByEmail($email);
    }

    public function getUserByPhoneNo(string $phone_no): User
    {
        return $this->repo->getUserByPhoneNo($phone_no);
    }

    public function getUserByIdentifier(string $identifier): User
    {
        return $this->repo->getUserByIdentifier($identifier);
    }

    /**
     * @param User $user
     * @param array|Collection|int|Role|string $roleIds
     */
    public function syncRoles($user, $roleIds): array
    {
        return $user->roles()->sync($roleIds);
    }
}
